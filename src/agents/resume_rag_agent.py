"""
Resume RAG Agent - Integration example for ResumeRetriever with LangChain agents.

This module demonstrates how to integrate the ResumeRetriever with LangChain
agents and chains to create a conversational AI that can answer questions
about people's skills, experience, and projects.
"""

import logging
import re
from collections import defaultdict, Counter
from typing import Dict, Any, List, Optional, Tuple

from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate, PromptTemplate
from langchain_core.runnables import RunnablePassthrough, RunnableLambda
from langchain_core.output_parsers import StrOutputParser
from langchain.tools.retriever import create_retriever_tool
from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import create_react_agent

from agents.resume_retriever import (
    ResumeRetriever,
    create_skill_retriever,
    create_work_experience_retriever,
    create_project_retriever,
    create_user_retriever
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ResumeRAGAgent:
    """
    Resume RAG Agent that combines semantic retrieval with conversational AI.
    
    This agent can answer questions about people's skills, work experience,
    and projects by retrieving relevant information from the resume database
    and generating contextual responses.
    """

    def __init__(
        self,
        model_name: str = "gpt-4.1-mini",
        model_provider: str = "openai",
        retriever_k: int = 5,
        similarity_threshold: float = 0.0
    ):
        """
        Initialize the Resume RAG Agent.
        
        Args:
            model_name: Name of the language model to use
            model_provider: Provider of the language model
            retriever_k: Number of documents to retrieve
            similarity_threshold: Minimum similarity score for retrieval
        """
        self.model_name = model_name
        self.model_provider = model_provider
        self.retriever_k = retriever_k
        self.similarity_threshold = similarity_threshold

        # Initialize the language model
        self.llm = init_chat_model(model_name, model_provider=model_provider)

        # Initialize the general retriever
        self.general_retriever = ResumeRetriever(
            k=retriever_k,
            similarity_threshold=similarity_threshold
        )

        # Create specialized retrievers
        self.skill_retriever = create_skill_retriever(
            k=retriever_k,
            similarity_threshold=similarity_threshold
        )
        self.work_retriever = create_work_experience_retriever(
            k=retriever_k,
            similarity_threshold=similarity_threshold
        )
        self.project_retriever = create_project_retriever(
            k=retriever_k,
            similarity_threshold=similarity_threshold
        )

        # Create retriever tools for agent use
        self.retriever_tools = self._create_retriever_tools()

        # Initialize the RAG chain
        self.rag_chain = self._create_rag_chain()

        # Initialize the agent
        self.agent = self._create_agent()

    def _create_retriever_tools(self) -> List:
        """Create LangChain tools from retrievers."""
        tools = []

        # General resume search tool
        general_tool = create_retriever_tool(
            self.general_retriever,
            "search_resumes",
            "Search through all resume data including skills, work experience, and projects. "
            "Use this for general queries about people's backgrounds."
        )
        tools.append(general_tool)

        # Skill-specific search tool
        skill_tool = create_retriever_tool(
            self.skill_retriever,
            "search_skills",
            "Search specifically for skills and technical expertise. "
            "Use this when looking for people with specific technical skills or experience levels."
        )
        tools.append(skill_tool)

        # Work experience search tool
        work_tool = create_retriever_tool(
            self.work_retriever,
            "search_work_experience",
            "Search through work experience and job history. "
            "Use this when looking for people who worked at specific companies or in specific roles."
        )
        tools.append(work_tool)

        # Project search tool
        project_tool = create_retriever_tool(
            self.project_retriever,
            "search_projects",
            "Search through project portfolios and achievements. "
            "Use this when looking for people who worked on specific types of projects."
        )
        tools.append(project_tool)

        return tools

    def _create_rag_chain(self):
        """
           Create a Retrieval-Augmented Generation (RAG) chain for question-answering.

           This method builds a LangChain pipeline that:
           - Retrieves relevant resume documents using the general retriever.
           - Formats the retrieved documents as context for the language model.
           - Uses a prompt template to instruct the model to answer questions based on the context.
           - Returns the generated answer as a string.

           Returns:
               A LangChain runnable chain for simple RAG-based Q\&A.
        """

        # Define the prompt template
        prompt = ChatPromptTemplate.from_messages([
            ("system", """You are a helpful HR assistant that can answer questions about people's skills, work experience, and projects based on resume data.

Use the provided context to answer questions accurately and comprehensively. If the context doesn't contain enough information to answer the question, say so clearly.

When mentioning people, always include their full name and relevant details from their resume.

Context:
{context}"""),
            ("human", "{question}")
        ])

        # Define the chain
        def format_docs(docs):
            """Format retrieved documents for the prompt."""
            if not docs:
                return "No relevant information found."

            formatted = []
            for doc in docs:
                metadata = doc.metadata
                formatted.append(
                    f"Person: {metadata.get('full_name', 'Unknown')}\n"
                    f"Type: {metadata.get('chunk_type', 'Unknown')}\n"
                    f"Content: {doc.page_content}\n"
                    f"Similarity Score: {metadata.get('similarity_score', 0):.3f}\n"
                )
            return "\n---\n".join(formatted)

        chain = (
            {"context": self.general_retriever | format_docs, "question": RunnablePassthrough()}
            | prompt
            | self.llm
            | StrOutputParser()
        )

        return chain

    def _create_agent(self):
        """Create a ReAct agent with retriever tools."""
        memory = MemorySaver()

        # System message for the agent
        system_message = """You are a helpful HR assistant that can search through resume data to answer questions about people's skills, work experience, and projects.

        You have access to several search tools:
        - search_resumes: For general queries about people's backgrounds
        - search_skills: For finding people with specific technical skills
        - search_work_experience: For finding people who worked at specific companies or roles
        - search_projects: For finding people who worked on specific types of projects
        
        Always use the most appropriate tool for each query. When presenting results, include the person's name and relevant details.
        
        If you can't find relevant information, suggest alternative search terms or approaches."""

        agent = create_react_agent(
            self.llm,
            self.retriever_tools,
            checkpointer=memory,
            state_modifier=system_message
        )

        return agent

    def query_simple(self, question: str) -> str:
        """
        Answer a question using the simple RAG chain.

        Args:
            question: The question to answer

        Returns:
            The answer as a string
        """
        try:
            logger.info(f"Processing simple query: {question}")
            response = self.rag_chain.invoke(question)
            return response
        except Exception as e:
            logger.error(f"Error in simple query: {str(e)}")
            return f"Sorry, I encountered an error while processing your question: {str(e)}"

    def query_agent(self, question: str, thread_id: str = "default") -> str:
        """
        Answer a question using the ReAct agent.

        Args:
            question: The question to answer
            thread_id: Thread ID for conversation memory

        Returns:
            The answer as a string
        """
        try:
            logger.info(f"Processing agent query: {question}")
            config = {"configurable": {"thread_id": thread_id}}

            response = self.agent.invoke(
                {"messages": [HumanMessage(content=question)]},
                config=config
            )

            # Extract the final message content
            if response and "messages" in response:
                final_message = response["messages"][-1]
                if hasattr(final_message, 'content'):
                    return final_message.content

            return "I couldn't generate a proper response."

        except Exception as e:
            logger.error(f"Error in agent query: {str(e)}")
            return f"Sorry, I encountered an error while processing your question: {str(e)}"

    def search_user_profile(self, user_id: str, query: str = "all information") -> str:
        """
        Search for information about a specific user.

        Args:
            user_id: The user ID to search for
            query: Specific query about the user

        Returns:
            Formatted information about the user
        """
        try:
            user_retriever = create_user_retriever(user_id=user_id, k=20)
            docs = user_retriever.invoke(query)

            if not docs:
                return f"No information found for user ID: {user_id}"

            # Group documents by type
            skills = []
            work_exp = []
            projects = []
            other = []

            user_name = "Unknown"
            user_email = "Unknown"

            for doc in docs:
                metadata = doc.metadata
                chunk_type = metadata.get('chunk_type', 'unknown')

                # Extract user info from first document
                if user_name == "Unknown":
                    user_name = metadata.get('full_name', 'Unknown')
                    user_email = metadata.get('email', 'Unknown')

                if chunk_type == 'skill_group':
                    skills.append(doc.page_content)
                elif chunk_type == 'work_experience':
                    work_exp.append(doc.page_content)
                elif chunk_type == 'project':
                    projects.append(doc.page_content)
                else:
                    other.append(doc.page_content)

            # Format the response
            response = f"Profile for {user_name} ({user_email})\n"
            response += "=" * 50 + "\n\n"

            if skills:
                response += "SKILLS:\n"
                for skill in skills:
                    response += f"• {skill}\n"
                response += "\n"

            if work_exp:
                response += "WORK EXPERIENCE:\n"
                for exp in work_exp:
                    response += f"• {exp}\n"
                response += "\n"

            if projects:
                response += "PROJECTS:\n"
                for project in projects:
                    response += f"• {project}\n"
                response += "\n"

            if other:
                response += "OTHER:\n"
                for item in other:
                    response += f"• {item}\n"

            return response

        except Exception as e:
            logger.error(f"Error searching user profile: {str(e)}")
            return f"Error retrieving profile for user {user_id}: {str(e)}"

    def _is_skill_matrix_request(self, query: str) -> bool:
        """
        Detect if the user is requesting a full skill matrix.

        Args:
            query: The user's query string

        Returns:
            True if this appears to be a skill matrix request
        """
        skill_matrix_patterns = [
            r'skill\s*matrix',
            r'full\s*skill',
            r'complete\s*skill',
            r'skill\s*profile',
            r'all\s*skills',
            r'comprehensive\s*skill',
            r'skill\s*overview',
            r'skill\s*summary',
            r'show.*skills',
            r'list.*skills'
        ]

        query_lower = query.lower()
        for pattern in skill_matrix_patterns:
            if re.search(pattern, query_lower):
                return True
        return False

    def _extract_user_id_from_query(self, query: str) -> Optional[str]:
        """
        Extract user ID from query if specified.

        Args:
            query: The user's query string

        Returns:
            User ID if found, None otherwise
        """
        # Look for patterns like "user 123", "user_id: 456", "for user abc"
        patterns = [
            r'user[_\s]*id[:\s]*([a-zA-Z0-9_-]+)',
            r'user[:\s]+([a-zA-Z0-9_-]+)',
            r'for\s+user\s+([a-zA-Z0-9_-]+)',
            r'of\s+user\s+([a-zA-Z0-9_-]+)'
        ]

        query_lower = query.lower()
        for pattern in patterns:
            match = re.search(pattern, query_lower)
            if match:
                return match.group(1)
        return None

    def generate_skill_matrix(self, user_id: str) -> str:
        """
        Generate a comprehensive skill matrix for a specific user.

        Args:
            user_id: The user ID to generate the skill matrix for

        Returns:
            Formatted skill matrix as markdown table
        """
        try:
            logger.info(f"Generating skill matrix for user: {user_id}")

            # Collect all data for the user with high k values to get comprehensive data
            skill_retriever = create_user_retriever(user_id=user_id, chunk_type="skill_group", k=50)
            work_retriever = create_user_retriever(user_id=user_id, chunk_type="work_experience", k=50)
            project_retriever = create_user_retriever(user_id=user_id, chunk_type="project", k=50)

            # Retrieve all data
            skill_docs = skill_retriever.invoke("skills technical expertise programming")
            work_docs = work_retriever.invoke("work experience job role responsibility")
            project_docs = project_retriever.invoke("project development implementation")

            if not skill_docs and not work_docs and not project_docs:
                return f"No data found for user ID: {user_id}"

            # Extract user information
            user_name = "Unknown"
            user_email = "Unknown"
            for doc in skill_docs + work_docs + project_docs:
                if user_name == "Unknown":
                    user_name = doc.metadata.get('full_name', 'Unknown')
                    user_email = doc.metadata.get('email', 'Unknown')
                    break

            # Analyze and categorize skills
            skill_analysis = self._analyze_skills(skill_docs, work_docs, project_docs)

            # Generate the formatted response
            return self._format_skill_matrix(user_name, user_email, skill_analysis)

        except Exception as e:
            logger.error(f"Error generating skill matrix: {str(e)}")
            return f"Error generating skill matrix for user {user_id}: {str(e)}"

    def _analyze_skills(self, skill_docs: List, work_docs: List, project_docs: List) -> Dict[str, Any]:
        """
        Analyze skills from different sources and categorize them.

        Args:
            skill_docs: Documents containing skill information
            work_docs: Documents containing work experience
            project_docs: Documents containing project information

        Returns:
            Dictionary containing analyzed skill data
        """
        skill_data = defaultdict(lambda: {
            'proficiency': 'Unknown',
            'years_experience': 'Unknown',
            'work_contexts': set(),
            'project_contexts': set(),
            'frequency': 0,
            'category': 'Other'
        })

        # Process skill documents
        for doc in skill_docs:
            content = doc.page_content.lower()
            chunk_subtype = doc.metadata.get('chunk_subtype', '')

            # Extract skills and experience levels
            skills = self._extract_skills_from_content(content)
            experience_level = self._extract_experience_level(content, chunk_subtype)

            for skill in skills:
                skill_data[skill]['frequency'] += 1
                skill_data[skill]['proficiency'] = experience_level
                skill_data[skill]['category'] = self._categorize_skill(skill)

                # Extract years of experience if mentioned
                years = self._extract_years_from_content(content)
                if years:
                    skill_data[skill]['years_experience'] = years

        # Process work experience documents
        for doc in work_docs:
            content = doc.page_content.lower()
            company = doc.metadata.get('chunk_subtype', 'Unknown Company')

            skills = self._extract_skills_from_content(content)
            for skill in skills:
                if skill in skill_data:
                    skill_data[skill]['work_contexts'].add(company)
                    skill_data[skill]['frequency'] += 1

        # Process project documents
        for doc in project_docs:
            content = doc.page_content.lower()
            project_name = doc.metadata.get('chunk_subtype', 'Unknown Project')

            skills = self._extract_skills_from_content(content)
            for skill in skills:
                if skill in skill_data:
                    skill_data[skill]['project_contexts'].add(project_name)
                    skill_data[skill]['frequency'] += 1

        # Convert sets to lists for easier handling
        for skill in skill_data:
            skill_data[skill]['work_contexts'] = list(skill_data[skill]['work_contexts'])
            skill_data[skill]['project_contexts'] = list(skill_data[skill]['project_contexts'])

        return dict(skill_data)

    def _extract_skills_from_content(self, content: str) -> List[str]:
        """Extract skill names from content text."""
        # Common technical skills patterns
        skill_patterns = [
            r'\b(python|java|javascript|typescript|c\+\+|c#|php|ruby|go|rust|swift|kotlin)\b',
            r'\b(react|angular|vue|django|flask|spring|express|laravel)\b',
            r'\b(mysql|postgresql|mongodb|redis|elasticsearch|cassandra)\b',
            r'\b(aws|azure|gcp|docker|kubernetes|jenkins|git|linux)\b',
            r'\b(machine learning|ai|data science|analytics|visualization)\b',
            r'\b(agile|scrum|devops|ci/cd|testing|debugging)\b'
        ]

        skills = set()
        content_lower = content.lower()

        for pattern in skill_patterns:
            matches = re.findall(pattern, content_lower)
            skills.update(matches)

        # Also extract capitalized words that might be technologies
        tech_words = re.findall(r'\b[A-Z][a-zA-Z]+\b', content)
        tech_skills = [word.lower() for word in tech_words if len(word) > 2]
        skills.update(tech_skills)

        return list(skills)

    def _extract_experience_level(self, content: str, chunk_subtype: str) -> str:
        """Extract experience level from content or chunk_subtype."""
        if chunk_subtype:
            if 'senior' in chunk_subtype.lower() or '5+' in chunk_subtype or '10+' in chunk_subtype:
                return 'Senior'
            elif 'junior' in chunk_subtype.lower() or '1-2' in chunk_subtype:
                return 'Junior'
            elif 'mid' in chunk_subtype.lower() or '3-5' in chunk_subtype:
                return 'Mid-level'

        # Look for experience indicators in content
        if re.search(r'(senior|lead|principal|architect)', content.lower()):
            return 'Senior'
        elif re.search(r'(junior|entry|beginner)', content.lower()):
            return 'Junior'
        elif re.search(r'(mid|intermediate)', content.lower()):
            return 'Mid-level'

        return 'Unknown'

    def _extract_years_from_content(self, content: str) -> str:
        """Extract years of experience from content."""
        year_patterns = [
            r'(\d+)\s*years?',
            r'(\d+)\s*yrs?',
            r'(\d+)-(\d+)\s*years?'
        ]

        for pattern in year_patterns:
            match = re.search(pattern, content.lower())
            if match:
                if len(match.groups()) == 2:  # Range pattern
                    return f"{match.group(1)}-{match.group(2)} years"
                else:
                    return f"{match.group(1)} years"

        return 'Unknown'

    def _categorize_skill(self, skill: str) -> str:
        """Categorize a skill into a logical group."""
        skill_lower = skill.lower()

        programming_languages = ['python', 'java', 'javascript', 'typescript', 'c++', 'c#', 'php', 'ruby', 'go', 'rust', 'swift', 'kotlin']
        frameworks = ['react', 'angular', 'vue', 'django', 'flask', 'spring', 'express', 'laravel', 'nodejs']
        databases = ['mysql', 'postgresql', 'mongodb', 'redis', 'elasticsearch', 'cassandra', 'sqlite']
        cloud_devops = ['aws', 'azure', 'gcp', 'docker', 'kubernetes', 'jenkins', 'git', 'linux', 'ci/cd', 'devops']
        data_ai = ['machine learning', 'ai', 'data science', 'analytics', 'visualization', 'tensorflow', 'pytorch']
        methodologies = ['agile', 'scrum', 'testing', 'debugging', 'tdd', 'bdd']

        if skill_lower in programming_languages:
            return 'Programming Languages'
        elif skill_lower in frameworks:
            return 'Frameworks & Libraries'
        elif skill_lower in databases:
            return 'Databases'
        elif skill_lower in cloud_devops:
            return 'Cloud & DevOps'
        elif skill_lower in data_ai:
            return 'Data Science & AI'
        elif skill_lower in methodologies:
            return 'Methodologies'
        else:
            return 'Other'

    def _format_skill_matrix(self, user_name: str, user_email: str, skill_analysis: Dict[str, Any]) -> str:
        """
        Format the skill analysis into a markdown table.

        Args:
            user_name: User's full name
            user_email: User's email
            skill_analysis: Analyzed skill data

        Returns:
            Formatted markdown string
        """
        if not skill_analysis:
            return f"# Skill Matrix for {user_name}\n\nNo skills found in the database."

        # Group skills by category
        categorized_skills = defaultdict(list)
        for skill, data in skill_analysis.items():
            category = data['category']
            categorized_skills[category].append((skill, data))

        # Sort skills within each category by frequency (most used first)
        for category in categorized_skills:
            categorized_skills[category].sort(key=lambda x: x[1]['frequency'], reverse=True)

        # Build the markdown response
        response = f"# 📊 Skill Matrix for {user_name}\n"
        response += f"**Email:** {user_email}\n\n"

        # Summary statistics
        total_skills = len(skill_analysis)
        categories = list(categorized_skills.keys())
        top_category = max(categorized_skills.keys(), key=lambda k: len(categorized_skills[k])) if categories else "None"

        response += "## 📈 Summary Statistics\n"
        response += f"- **Total Skills:** {total_skills}\n"
        response += f"- **Skill Categories:** {len(categories)}\n"
        response += f"- **Top Category:** {top_category} ({len(categorized_skills.get(top_category, []))} skills)\n\n"

        # Main skill matrix table
        response += "## 🔧 Complete Skill Matrix\n\n"
        response += "| Skill Category | Skill Name | Proficiency | Years Experience | Work Context | Project Context | Usage Frequency |\n"
        response += "|---|---|---|---|---|---|---|\n"

        for category in sorted(categorized_skills.keys()):
            skills_in_category = categorized_skills[category]
            for i, (skill, data) in enumerate(skills_in_category):
                work_context = ', '.join(data['work_contexts'][:2]) if data['work_contexts'] else 'N/A'
                if len(data['work_contexts']) > 2:
                    work_context += f" (+{len(data['work_contexts'])-2} more)"

                project_context = ', '.join(data['project_contexts'][:2]) if data['project_contexts'] else 'N/A'
                if len(data['project_contexts']) > 2:
                    project_context += f" (+{len(data['project_contexts'])-2} more)"

                # Only show category name for the first skill in each category
                category_display = category if i == 0 else ""

                response += f"| {category_display} | **{skill.title()}** | {data['proficiency']} | {data['years_experience']} | {work_context} | {project_context} | {data['frequency']} |\n"

        # Category breakdown
        response += "\n## 📋 Skills by Category\n\n"
        for category in sorted(categorized_skills.keys()):
            skills_in_category = categorized_skills[category]
            response += f"### {category}\n"
            for skill, data in skills_in_category:
                response += f"- **{skill.title()}** ({data['proficiency']}) - Used {data['frequency']} times\n"
            response += "\n"

        return response


# Example usage and testing functions
def demo_resume_rag_agent():
    """Demonstrate the Resume RAG Agent functionality."""
    print("🚀 Resume RAG Agent Demo")
    print("=" * 50)

    try:
        # Initialize the agent
        agent = ResumeRAGAgent()

        # Example queries
        queries = [
            "Who has Python programming skills?",
            "Find people with machine learning experience",
            "Show me software engineers who worked at tech companies",
            "What projects involve data science or AI?",
            "Who has 5-10 years of experience in backend development?"
        ]

        # Skill matrix queries
        skill_matrix_queries = [
            "show skill matrix for user 123",
            "generate full skill profile for user_id: test_user",
            "complete skill overview"
        ]

        print("\n🤖 Testing Simple RAG Chain:")
        for i, query in enumerate(queries[:2], 1):
            print(f"\n{i}. Query: {query}")
            try:
                response = agent.query_simple(query)
                print(f"Response: {response[:200]}...")
            except Exception as e:
                print(f"Error: {str(e)}")

        print("\n🤖 Testing ReAct Agent:")
        for i, query in enumerate(queries[2:4], 1):
            print(f"\n{i}. Query: {query}")
            try:
                response = agent.query_agent(query)
                print(f"Response: {response[:200]}...")
            except Exception as e:
                print(f"Error: {str(e)}")

        print("\n🔧 Testing Skill Matrix Generation:")
        for i, query in enumerate(skill_matrix_queries[:2], 1):
            print(f"\n{i}. Query: {query}")
            try:
                # Test skill matrix detection
                is_matrix_request = agent._is_skill_matrix_request(query)
                print(f"Detected as skill matrix request: {is_matrix_request}")

                if is_matrix_request:
                    user_id = agent._extract_user_id_from_query(query)
                    print(f"Extracted user ID: {user_id}")

                    if user_id:
                        response = agent.generate_skill_matrix(user_id)
                        print(f"Skill Matrix Response: {response[:300]}...")
                    else:
                        print("No user ID found in query")
            except Exception as e:
                print(f"Error: {str(e)}")

        print("\n✅ Demo completed successfully!")

    except Exception as e:
        print(f"❌ Demo failed: {str(e)}")


# LangGraph-compatible wrapper for integration with agents.py
def create_resume_rag_graph():
    """
    Create a LangGraph Pregel object for the Resume RAG Agent.

    This function creates a LangGraph-compatible agent that can be integrated
    into the main agent system in agents.py.

    Returns:
        Pregel: A compiled LangGraph agent
    """
    from typing import TypedDict, Annotated
    from langgraph.graph import StateGraph, add_messages, START, END
    from langgraph.checkpoint.memory import MemorySaver
    from langchain_core.messages import AIMessage

    class ResumeRAGState(TypedDict):
        """State for Resume RAG agent."""
        messages: Annotated[list, add_messages]

    # Initialize the Resume RAG Agent instance
    resume_agent = ResumeRAGAgent()

    def resume_rag_node(state: ResumeRAGState) -> ResumeRAGState:
        """
        Process user messages using the Resume RAG Agent.

        Args:
            state: Current state containing messages

        Returns:
            Updated state with agent response
        """
        messages = state["messages"]
        if not messages:
            return {"messages": [AIMessage(content="Hello! I can help you search through resume data. What would you like to know?")]}

        # Get the last human message
        last_message = messages[-1]
        if hasattr(last_message, 'content'):
            user_query = last_message.content
        else:
            user_query = str(last_message)

        try:
            # Check if this is a skill matrix request
            if resume_agent._is_skill_matrix_request(user_query):
                logger.info("Detected skill matrix request")

                # Extract user ID from query
                user_id = resume_agent._extract_user_id_from_query(user_query)

                if user_id:
                    # Generate skill matrix for specific user
                    response = resume_agent.generate_skill_matrix(user_id)
                else:
                    # Ask for user ID if not provided
                    response = ("I'd be happy to generate a skill matrix! However, I need a user ID to create a comprehensive skill matrix. "
                              "Please specify a user ID in your request, for example: 'show skill matrix for user 123' or 'generate full skill profile for user_id: abc123'")

                return {"messages": [AIMessage(content=response)]}
            else:
                # Use the regular agent's query method for other requests
                response = resume_agent.query_agent(user_query)
                return {"messages": [AIMessage(content=response)]}
        except Exception as e:
            logger.error(f"Error in resume RAG node: {str(e)}")
            return {"messages": [AIMessage(content=f"Sorry, I encountered an error: {str(e)}")]}

    # Build the graph
    graph_builder = StateGraph(ResumeRAGState)
    graph_builder.add_node("resume_rag", resume_rag_node)
    graph_builder.add_edge(START, "resume_rag")
    graph_builder.add_edge("resume_rag", END)

    # Compile with memory
    memory = MemorySaver()
    return graph_builder.compile(checkpointer=memory)


# Create the graph instance for export
resume_rag_agent = create_resume_rag_graph()


if __name__ == "__main__":
    demo_resume_rag_agent()
